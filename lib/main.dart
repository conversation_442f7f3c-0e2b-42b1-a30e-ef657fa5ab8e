import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:healo/common/utils/size.dart';
import 'package:healo/constants/app_colors.dart';
import 'package:healo/firebase_options.dart';
import 'package:healo/route/router.dart';
import 'package:healo/screens/splash_screen.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:healo/providers/theme_provider.dart';
import 'package:healo/providers/auth_state_manager.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  FirebaseFirestore.instance.settings =
      const Settings(persistenceEnabled: true);
  runApp(
    ProviderScope(
      child: const MyApp(),
    ),
  );
}

class MyApp extends ConsumerWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isDarkMode = ref.watch(themeModeProvider);

    // Initialize auth state manager
    ref.watch(authStateManagerProvider);

    MySize().init(context);
    SizeConfig().init(context);

    return MaterialApp(
        title: 'Healo',
        debugShowCheckedModeBanner: false,
        theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(seedColor: Colors.black),
          scaffoldBackgroundColor: AppColors.white,
          cardColor: AppColors.white100,
          useMaterial3: true,
          appBarTheme: AppBarTheme(
              elevation: 0,
              backgroundColor: AppColors.white,
              foregroundColor: AppColors.black),
          textTheme: TextTheme(
            bodySmall: GoogleFonts.poppins(
              color: AppColors.textGray,
            ),
            bodyMedium: GoogleFonts.poppins(
              color: AppColors.black,
            ),
            bodyLarge: GoogleFonts.poppins(
              color: AppColors.black,
            ),
          ),
        ),
        darkTheme: ThemeData(
            colorScheme: ColorScheme.fromSeed(seedColor: Colors.black),
            scaffoldBackgroundColor: AppColors.black,
            useMaterial3: true,
            cardColor: AppColors.black100,
            appBarTheme: AppBarTheme(
                elevation: 0,
                backgroundColor: AppColors.black,
                foregroundColor: AppColors.white),
            textTheme: TextTheme(
              bodySmall: GoogleFonts.poppins(
                color: AppColors.white,
              ),
              bodyMedium: GoogleFonts.poppins(
                color: AppColors.white,
              ),
              bodyLarge: GoogleFonts.poppins(
                color: AppColors.white,
              ),
            )),
        themeMode: isDarkMode ? ThemeMode.dark : ThemeMode.light,
        onGenerateRoute: (settings) => generateRoute(settings),
        home: SplashScreen());
  }
}
