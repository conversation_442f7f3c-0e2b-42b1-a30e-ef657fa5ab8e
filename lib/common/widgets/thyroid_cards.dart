import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:healo/common/utils/size.dart';
import 'package:healo/constants/app_colors.dart';

class ThyroidCard extends StatelessWidget {
  const ThyroidCard({
    super.key,
    required this.title,
    required this.value,
    required this.icon,
    required this.unit,
  });

  final String title;
  final String value;
  final String icon;
  final String unit;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      child: Card(
        color: Theme.of(context).cardColor,
        shape: RoundedRectangleBorder(
            side: BorderSide(color: AppColors.primaryColor),
            borderRadius: Shape.circular(MySize.size40)),
        child: Padding(
          padding: EdgeInsets.symmetric(
              vertical: MySize.size20, horizontal: MySize.size20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                height: MySize.size45,
                width: MySize.size45,
                decoration: BoxDecoration(
                  color: AppColors.primaryColor,
                  borderRadius: BorderRadius.circular(50),
                ),
                padding: EdgeInsets.all(MySize.size5),
                child: SvgPicture.asset(
                  icon,
                  height: title == "Free T4" ? MySize.size40 : MySize.size24,
                  width: title == "Free T4" ? MySize.size40 : MySize.size24,
                ),
              ),
              Space.height(10),
              Text(
                title,
                style: TextStyle(
                  fontSize: MySize.size12,
                  color: AppColors.textGray,
                ),
              ),
              Space.height(4),
              Text(
                value,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: MySize.size20,
                ),
              ),
              Space.height(4),
              Text(
                unit,
                style: TextStyle(
                  fontSize: MySize.size14,
                  color: AppColors.primaryColor,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
