import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:healo/services/firestore_service.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'dart:developer';

// Create a provider that forces refresh when auth state changes
final authStateProvider = StreamProvider<User?>((ref) {
  return FirebaseAuth.instance.authStateChanges();
});

// User data provider that properly handles auth state changes
final userDataProvider = FutureProvider<Map<String, dynamic>?>((ref) async {
  final authState = ref.watch(authStateProvider);

  return authState.when(
    data: (user) async {
      if (user == null) {
        log("No user logged in, returning null user data");
        return null;
      }

      log("User logged in: ${user.phoneNumber}, fetching user data");
      try {
        final userData = await FirestoreService().getUser();
        if (userData != null) {
          return {
            'name': userData.name,
            'gender': userData.gender,
            'phone': user.phoneNumber,
            'dob': userData.dob,
            'weight': userData.weight,
            'height': userData.height,
            'initialDataCompleted': userData.initialDataCompleted,
          };
        }
        return null;
      } catch (e) {
        log("Error fetching user data: $e");
        return null;
      }
    },
    loading: () => null,
    error: (error, stack) {
      log("Auth state error: $error");
      return null;
    },
  );
});

// Individual user providers that depend on the main user data provider
final userNameProvider = Provider<String?>((ref) {
  final userData = ref.watch(userDataProvider);
  return userData.when(
    data: (data) => data?['name'] as String?,
    loading: () => null,
    error: (error, stack) => null,
  );
});

final userGenderProvider = Provider<String?>((ref) {
  final userData = ref.watch(userDataProvider);
  return userData.when(
    data: (data) => data?['gender'] as String?,
    loading: () => null,
    error: (error, stack) => null,
  );
});

final userPhoneProvider = Provider<String?>((ref) {
  final userData = ref.watch(userDataProvider);
  return userData.when(
    data: (data) => data?['phone'] as String?,
    loading: () => null,
    error: (error, stack) => null,
  );
});

final userDobProvider = Provider<String?>((ref) {
  final userData = ref.watch(userDataProvider);
  return userData.when(
    data: (data) => data?['dob'] as String?,
    loading: () => null,
    error: (error, stack) => null,
  );
});

final userWeightProvider = Provider<num?>((ref) {
  final userData = ref.watch(userDataProvider);
  return userData.when(
    data: (data) => data?['weight'] as num?,
    loading: () => null,
    error: (error, stack) => null,
  );
});

final userHeightProvider = Provider<num?>((ref) {
  final userData = ref.watch(userDataProvider);
  return userData.when(
    data: (data) => data?['height'] as num?,
    loading: () => null,
    error: (error, stack) => null,
  );
});

final userInitialDataCompletedProvider = Provider<bool>((ref) {
  final userData = ref.watch(userDataProvider);
  return userData.when(
    data: (data) => data?['initialDataCompleted'] as bool? ?? false,
    loading: () => false,
    error: (error, stack) => false,
  );
});
