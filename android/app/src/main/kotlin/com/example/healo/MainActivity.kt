package com.example.healo

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import io.flutter.embedding.android.FlutterFragmentActivity

class MainActivity: FlutterFragmentActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Handle the privacy policy intent from Health Connect
        if (intent?.action == "android.intent.action.VIEW_PERMISSION_USAGE") {
            // Open the privacy policy URL directly
            val privacyPolicyUrl = "https://thehelthy.co/privacy-policy"
            val browserIntent = Intent(Intent.ACTION_VIEW, Uri.parse(privacyPolicyUrl))
            startActivity(browserIntent)

            // Note: We're not finishing the activity because we want the user
            // to be able to return to the app after viewing the privacy policy
        }
    }
}
